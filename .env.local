NODE_ENV="test"

# Local development configuration
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD=""
REDIS_USERNAME=""

# Local MongoDB
MONGO_DB_URI="mongodb://localhost:27017/pixelpark_test"

# Local Elasticsearch
ELASTICSEARCH_NODE=http://localhost:9200
ELASTICSEARCH_USERNAME=""
ELASTICSEARCH_PASSWORD=""

# Blockchain Configuration (using public RPC for testing)
MAINNET_RPC=https://rpc-pulsechain.g4mm4.io
MULTICALL_MAIN=0xA56E476eA43944462c329875c383a8B62f6dC169
ROYALTY_FEE_MANAGER=0xe59428e166943C00630FAED33fdBF97bc62d2755
PIXELPARK_EXCHANGE_ADDRESS=0x23f8505123A573F1360E1cb3e5d83d7Ae01d23B3

# Testing configuration
HARD_START=false
START_BLOCK=21000000

# Port
PORT=3001

# AWS (optional for testing)
AWS_REGION="us-east-1"
AWS_S3_BUCKET="pixelpark-dev"
