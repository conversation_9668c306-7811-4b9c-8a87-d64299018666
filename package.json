{"name": "cron-nestjs-pixelpark", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "NODE_OPTIONS='--trace-warnings --trace-deprecation' nest start --watch", "start:qa": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "nest build && node dist/main", "start:safe": "nest build && node scripts/safe-start.js", "start:memory-safe": "nest build && node scripts/memory-safe-start.js", "start:debug-core": "nest build && node scripts/debug-start.js", "start:gdb": "chmod +x scripts/run-with-gdb.sh && scripts/run-with-gdb.sh", "docker:debug": "docker-compose -f docker-compose.yml -f docker-compose.debug.yml up", "memory:check": "NODE_OPTIONS='--expose-gc' node scripts/memory-leak-check.js", "memory:test": "node scripts/test-memory-fixes.js", "memory:test-workers": "NODE_OPTIONS='--expose-gc' node scripts/test-worker-memory.js", "debug:setup": "chmod +x scripts/setup-debug-env.sh && scripts/setup-debug-env.sh", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "configure-husky": "npx husky install && npx husky add .husky/commit-msg \"npx --no-install commitlint --edit $1\" && npx husky add .husky/pre-commit \"npx --no-install lint-staged\""}, "dependencies": {"@elastic/elasticsearch": "^9.0.2", "@nestjs/bull": "^11.0.2", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.2.0", "@nestjs/core": "^9.0.0", "@nestjs/mongoose": "^9.2.0", "@nestjs/platform-express": "^9.0.0", "@nestjs/schedule": "^2.1.0", "aws-sdk": "^2.1177.0", "axios": "^0.27.2", "bull": "^4.16.5", "cache-manager": "^4.1.0", "cache-manager-redis-store": "^2.0.0", "ethereum-multicall": "^2.26.0", "ethers": "^6.7.1", "ioredis": "^5.6.1", "moment": "^2.29.4", "mongoose": "^6.4.4", "nest-winston": "^1.9.7", "node-schedule": "^2.1.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "web3": "^1.7.4", "winston": "^3.13.1"}, "devDependencies": {"@commitlint/cli": "^17.3.0", "@compodoc/compodoc": "^1.1.25", "@nestjs/cli": "^9.5.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/cache-manager": "^4.0.1", "@types/cache-manager-redis-store": "^2.0.1", "@types/express": "^4.17.13", "@types/jest": "28.1.4", "@types/node": "^16.0.0", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.48.0", "@typescript-eslint/parser": "^5.48.0", "commitlint-config-jira": "^1.6.4", "commitlint-plugin-jira-rules": "^1.6.4", "eslint": "^8.31.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^8.0.3", "jest": "28.1.2", "lint-staged": "^13.1.0", "prettier": "^2.8.1", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.5", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.0.0", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "lint-staged": {"**/*.{ts,tsx}": ["npx prettier --write", "npx eslint --fix"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}