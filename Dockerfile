FROM node:20-slim AS builder

RUN apt-get update && apt-get install -y python3 make g++ gdb lldb valgrind

# Set working directory
WORKDIR /app

# Copy only essential files for dependency install
COPY package*.json ./
COPY tsconfig.* ./

# Install dependencies (use npm ci if package-lock.json exists)
RUN npm install

# Copy the rest of the application code
COPY . .

# Build the application
RUN npm run build -- --config tsconfig.build.json


FROM node:20-slim

WORKDIR /app

# Copy built app and node_modules from builder stage
COPY --from=builder /app /app

ENV NODE_OPTIONS="--max-old-space-size=4096 --expose-gc --trace-warnings --trace-uncaught --unhandled-rejections=strict"

# Expose port (optional if needed by infra)
# EXPOSE 3000

RUN mkdir -p /app/logs

# Start the application with memory-safe wrapper
CMD ["npm", "run", "start:memory-safe"]
