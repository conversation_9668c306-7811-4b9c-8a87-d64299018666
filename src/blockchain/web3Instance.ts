import Web3 from "web3";
import { ethers } from "ethers";
import { ConfigService } from "@nestjs/config";
import { Injectable } from "@nestjs/common";

@Injectable()
export class Web3Instance {
  private RPC: any;
  private web3Instance: Web3 | null = null;
  private etherInstance: ethers.JsonRpcProvider | null = null;
  private instanceId: string;

  constructor(private configService: ConfigService) {
    this.RPC = this.configService.get("MAINNET_RPC");
    this.instanceId = `${process.pid}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  getWeb3Instance(): Web3 {
    // Create per-instance Web3 to avoid cross-worker contamination
    if (!this.web3Instance) {
      this.web3Instance = new Web3(this.RPC);
      console.log(`Created Web3 instance for ${this.instanceId}`);
    }
    return this.web3Instance;
  }

  getEtherInstance(): ethers.JsonRpcProvider {
    // Create per-instance Ethers to avoid cross-worker contamination
    if (!this.etherInstance) {
      this.etherInstance = new ethers.JsonRpcProvider(this.RPC, undefined, {
        // Add connection limits to prevent memory leaks
        batchMaxCount: 50, // Reduced from 100
        batchMaxSize: 512 * 1024, // Reduced to 512KB
        batchStallTime: 10, // 10ms
      });
      console.log(`Created Ethers instance for ${this.instanceId}`);
    }
    return this.etherInstance;
  }

  getRPC(): string {
    return this.RPC;
  }

  // Method to safely cleanup instances for this specific instance
  cleanup(): void {
    try {
      console.log(`Cleaning up Web3Instance ${this.instanceId}`);

      if (this.etherInstance) {
        // Ethers cleanup
        try {
          this.etherInstance.destroy();
        } catch (error) {
          console.warn(`Error destroying Ethers instance ${this.instanceId}:`, error);
        }
        this.etherInstance = null;
      }

      if (this.web3Instance) {
        // Web3 cleanup - be careful with this in worker threads
        try {
          const provider = this.web3Instance.currentProvider;
          if (provider && typeof provider === 'object' && 'disconnect' in provider) {
            (provider as any).disconnect();
          }
        } catch (error) {
          console.warn(`Error disconnecting Web3 provider ${this.instanceId}:`, error);
        }
        this.web3Instance = null;
      }

      console.log(`Web3Instance cleanup completed for ${this.instanceId}`);
    } catch (error) {
      console.error(`Error during Web3Instance cleanup for ${this.instanceId}:`, error);
    }
  }

  // Get instance ID for monitoring
  getInstanceId(): string {
    return this.instanceId;
  }
}
