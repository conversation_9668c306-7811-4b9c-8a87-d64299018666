import { QueueService } from "../queue/queue.service";
import { Web3Instance } from "../../blockchain/web3Instance";
import { BlockChainConstant } from "../../blockchain/constants";
import { EntityType } from "../../shared/enums/entity-type.enum";
import { TokenStandard } from "../../shared/enums/token-standard.enum";
import { MulticallService, ContractCall } from "../helper/multicall.service";
import { BlockData, BlockDataDocument, BlockDataSchema } from "../../database/schemas/block-data.schema";

import axios from "axios";
import moment from "moment";
import mongoose from "mongoose";
import { Cache } from "cache-manager";
import { ConfigService } from "@nestjs/config";
import { createLogger, format, transports } from "winston";
import { parentPort, workerData } from "worker_threads";

class WorkerService {
  private blockStart: number;
  private blockSize: number;
  private workerId: number;

  private logger: any;

  private web3Instance: any;
  private etherInstance: any;
  private royaltyFeeManager: any;
  private multicallService: MulticallService;
  private web3InstanceModule: Web3Instance;

  private cacheManager: Cache;

  private gateway = "https://gateway.pinata.cloud/ipfs/";
  private CID_REGEX = /^Qm[1-9A-HJ-NP-Za-km-z]{44}$/;
  private CONTRACT_DATA_PREFIX = "contract:";

  constructor(blockStart: number, blockSize: number, workerId: number, private readonly queueService: QueueService) {
    this.blockStart = blockStart;
    this.blockSize = blockSize;
    this.workerId = workerId;

    // Create a Winston logger first
    this.logger = createLogger({
      level: "info",
      format: format.combine(format.timestamp(), format.json()),
      transports: [new transports.Console()],
    });

    try {
      // Wrap Web3 initialization in try-catch to prevent native module crashes
      this.logger.info(`Worker ${workerId}: Initializing Web3 instances...`);

      // Log memory before initialization
      const memBefore = process.memoryUsage();
      this.logger.info(`Worker ${workerId}: Memory before Web3 init: ${Math.round(memBefore.rss / 1024 / 1024)}MB RSS`);

      // Force garbage collection before native module initialization if available
      if (global.gc) {
        global.gc();
      }

      // Create a ConfigService instance to pass to Web3Instance
      const configService = new ConfigService();
      this.web3InstanceModule = new Web3Instance(configService);

      // Use per-instance Web3 instances to prevent cross-worker contamination
      this.web3Instance = this.web3InstanceModule.getWeb3Instance();
      this.etherInstance = this.web3InstanceModule.getEtherInstance();
      this.royaltyFeeManager = configService.get("ROYALTY_FEE_MANAGER");

      // Initialize MulticallService with required dependencies
      this.multicallService = new MulticallService(this.logger, configService, this.web3InstanceModule);

      void this.multicallService.onModuleInit();

      // Log memory after initialization
      const memAfter = process.memoryUsage();
      this.logger.info(`Worker ${workerId}: Memory after Web3 init: ${Math.round(memAfter.rss / 1024 / 1024)}MB RSS`);
      this.logger.info(`Worker ${workerId}: Web3 instances initialized successfully`);
    } catch (error) {
      this.logger.error(`Worker ${workerId}: Failed to initialize Web3 instances: ${error.message}`);
      throw error;
    }
  }

  public async run(): Promise<void> {
    this.logger.info(`[worker-task.ts] Working on ${this.blockStart}-${this.blockStart + this.blockSize - 1}`);

    try {
      // const result = await this.blockSync(this.blockStart, this.workerId, this.blockSize);

      parentPort?.postMessage({
        workerId: this.workerId,
        collections: [],
        tokens: [],
        blockNumber: 3, //result.blockNumber,
      });
    } catch (error) {
      this.logger.error(`Worker ${this.workerId} failed: ${error?.message}`);
      parentPort?.postMessage({
        workerId: this.workerId,
        error: error?.message,
        collections: [],
        tokens: [],
        blockNumber: this.blockStart,
      });
    } finally {
      await this.cleanup();
    }
  }

  public async cleanup(): Promise<void> {
    try {
      this.logger.info(`[worker-task.ts] Cleaning up worker ${this.workerId}`);

      // Log memory before cleanup
      const memBefore = process.memoryUsage();
      this.logger.info(`Worker ${this.workerId}: Memory before cleanup: ${Math.round(memBefore.rss / 1024 / 1024)}MB RSS`);

      // Clear MulticallService reference (no explicit cleanup method available)
      if (this.multicallService) {
        this.logger.info(`Worker ${this.workerId}: Clearing MulticallService reference`);
      }

      // Minimal Web3 cleanup - just clear references, don't call native cleanup
      if (this.web3InstanceModule) {
        try {
          // Only cleanup if it's safe to do so
          this.logger.info(`Worker ${this.workerId}: Clearing Web3Instance references`);
          // Don't call cleanup() to avoid native module issues
        } catch (error) {
          this.logger.warn(`Worker ${this.workerId}: Error during Web3Instance cleanup: ${error.message}`);
        }
      }

      // Set references to null to allow GC
      this.web3Instance = null;
      this.etherInstance = null;
      this.royaltyFeeManager = null;
      this.multicallService = null;
      this.web3InstanceModule = null;

      // Force garbage collection if available
      if (global.gc) {
        this.logger.info(`Worker ${this.workerId}: Forcing garbage collection`);
        global.gc();
      }

      // Log memory after cleanup
      const memAfter = process.memoryUsage();
      this.logger.info(`Worker ${this.workerId}: Memory after cleanup: ${Math.round(memAfter.rss / 1024 / 1024)}MB RSS`);
      this.logger.info(`Worker ${this.workerId}: Memory freed: ${Math.round((memBefore.rss - memAfter.rss) / 1024 / 1024)}MB`);

      // Small delay to allow cleanup to complete
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      this.logger.error(`Cleanup error in worker ${this.workerId}: ${error?.message}`);
    }
  }

  /**
   * Synchronizes blockchain blocks by fetching and processing them in batches.
   *
   * This method retrieves the current block number from the Ethereum instance and processes blocks
   * from the previous block number to the current block number in batches of a specified size.
   * Each block's transactions are fetched and their receipts are processed.
   *
   * @async
   * @function blockSync
   * @returns {Promise<void>} A promise that resolves when the block synchronization is complete.
   *
   * @throws {Error} If there is an error during the block synchronization process.
   *
   * @example
   * // Example usage:
   * await blockSync();
   *
   * @remarks
   * - The method uses a batch size of 10 blocks.
   * - Each block's transactions are fetched and their receipts are processed.
   * - The block number is cached after processing each batch.
   * - Errors during the block synchronization process are logged.
   */
  async blockSync(startingBlock: number, workerId: number, batchSize = 10) {
    try {
      // Limit processing to MAX_BLOCKS_PER_CRON blocks per cron job
      const targetBlock = startingBlock + batchSize - 1;

      const finalTokens = [];
      const finalCollections = [];

      const result = {
        blockNumber: targetBlock,
        tokens: [],
        collections: [],
      };

      await Promise.all(
        // eslint-disable-next-line @typescript-eslint/naming-convention
        Array.from({ length: targetBlock - startingBlock + 1 }, (_, i) => startingBlock + i).map(async blockData => {
          try {
            const blockInfo = await this.etherInstance.getBlock(blockData);
            const [collections, tokens] = await this.fetchTransactionReceipt(blockInfo.transactions, blockInfo.number, blockInfo.timestamp);
            finalTokens.push(...tokens);
            finalCollections.push(...collections);
            return { collections, tokens };
          } catch (error) {
            this.logger.info(`blockSync error at block ${blockData}: ${error?.message}`, error);
            return { collections: [], tokens: [] };
          }
        }),
      );
      this.logger.info(` ********* Finished Indexing block: ${targetBlock} ********* `);

      result.tokens = finalTokens;
      result.collections = finalCollections;
      await this.saveCollectionAndToken(finalCollections, finalTokens);

      return {
        blockNumber: targetBlock,
        tokens: finalTokens || [],
        collections: finalCollections || [],
      };
    } catch (error) {
      this.logger.error(`blockSync ${error?.message}`, error);
      throw error;
    }
  }

  /**
   * Fetches the transaction receipts for the given transactions, processes them to extract collection and token information,
   * and saves the results if any relevant data is found.
   *
   * @param {string[]} transactions - An array of transaction hashes to fetch receipts for.
   * @param {number} blockNumber - The block number associated with the transactions.
   * @param {number} timestamp - The timestamp associated with the block.
   * @returns {Promise<void>} - A promise that resolves when the processing is complete.
   *
   * @throws Will log an error if fetching transaction receipts or processing them fails.
   *
   * @example
   * ```typescript
   * const transactions = ["0x123...", "0x456..."];
   * const blockNumber = 123456;
   * const timestamp = 1627890123;
   * await fetchTransactionReceipt(transactions, blockNumber, timestamp);
   * ```
   */
  async fetchTransactionReceipt(transactions: string[], blockNumber: number, timestamp: number) {
    // Checks if transactions is not any error message. it is an array
    try {
      if (transactions.length) {
        let collectionResult: any = [];
        let tokenResult: any = [];
        // Process transactions in parallel
        await Promise.all(
          transactions.map(async transactionHash => {
            try {
              const transactionRecipt = await this.etherInstance.getTransactionReceipt(transactionHash);
              if (transactionRecipt && transactionRecipt.to && transactionRecipt.logs.length) {
                const toCode: string = await this.etherInstance.getCode(transactionRecipt.to);
                /* Checks if it is collection contract */
                if (toCode !== "0x") {
                  const { collectionInfo, tokenInfo } = await this.getContractData(transactionRecipt, blockNumber, timestamp);
                  if (collectionInfo && collectionInfo.length > 0) collectionResult = collectionResult.concat(...collectionInfo);
                  if (tokenInfo && tokenInfo.length > 0) tokenResult = tokenResult.concat(...tokenInfo);
                }
              }
            } catch (error) {
              this.logger.error(`fetchTransactionReceipt ${error?.message}`, error);
            }
          }),
        );
        this.logger.info(JSON.stringify({ blockNumber, collLen: collectionResult.length, tokLen: tokenResult.length }));
        if (collectionResult.length > 0 || tokenResult.length > 0) {
          return [collectionResult, tokenResult];
        }
      } else {
        this.logger.info(JSON.stringify({ blockNumber, collLen: 0, tokLen: 0, transaction: 0 }));
      }
      return [[], []];
    } catch (error) {
      this.logger.error(`fetchTransactionReceipt ${error?.message}`);
    }
  }

  /**
   * Retrieves contract data from a given transaction receipt, block number, and timestamp.
   *
   * This function processes the logs from the transaction receipt to gather token standard information,
   * performs multicall operations to check for supported interfaces, and fetches additional contract details
   * such as collection metadata and royalty metadata.
   *
   * @param transactionRecipt - The receipt of the transaction containing logs to be processed.
   * @param blockNumber - The block number associated with the transaction.
   * @param timestamp - The timestamp of the transaction.
   *
   * @returns A promise that resolves to an object containing collection information and token information.
   *
   * @throws Will throw an error if the logs array is empty or if any of the asynchronous operations fail.
   *
   * @example
   * ```typescript
   * const transactionRecipt = await web3.eth.getTransactionReceipt(txHash);
   * const blockNumber = 123456;
   * const timestamp = Date.now();
   * const contractData = await getContractData(transactionRecipt, blockNumber, timestamp);
   * console.log(contractData.collectionInfo, contractData.tokenInfo);
   * ```
   */
  async getContractData(transactionRecipt, blockNumber: number, timestamp: number) {
    const logs = transactionRecipt.logs;
    /* This is to make sure that transaction log we fetched is Array and not any error message */
    if (logs.length) {
      const tokenStandardInfos = [];
      const transactionInfos = [];
      const supportInterfaceMapping = {};
      const supportInterfaceBatch = [];
      // this.logger.info("**************************** Get Contract Data ****************************");
      for (let k = 0; k < logs.length; k++) {
        const transactionInfo = logs[k];
        const tokenStandard = await this.getTokenStandardInfo(transactionInfo, timestamp);
        // this.logger.info("**************************** Get Token Standard Info ****************************");
        if (tokenStandard) {
          tokenStandardInfos.push(tokenStandard);
          transactionInfos.push(transactionInfo);
          if (!supportInterfaceMapping[transactionInfo.address]) {
            const supportData: ContractCall = {
              contractAddress: transactionInfo.address,
              methodCalls: [
                {
                  reference: "supportsInterface",
                  methodName: "supportsInterface",
                  methodParameters: [tokenStandard.interfaces],
                },
              ],
            };
            supportInterfaceBatch.push(supportData);
            supportInterfaceMapping[transactionInfo.address] = true;
          }
        }
      }
      // this.logger.info("**************************** Multicall for contract Interface ****************************");
      const result = await this.multicallService.performMulticall(supportInterfaceBatch);

      const collectionMetadata = [];
      const royaltyMetadata = [];
      const options = [];
      const tokenDetailsMapping = {};

      for (let k = 0; k < tokenStandardInfos.length; k++) {
        const singleTokenInfo = tokenStandardInfos[k];
        const transactionInfo = transactionInfos[k];
        const interfaceSupported = this.multicallService.parseResult(result, singleTokenInfo.contractAddress, "supportsInterface");

        if (interfaceSupported) {
          const option = {
            ...singleTokenInfo,
            transactionInfo,
            block: blockNumber,
            supportsInterface: interfaceSupported,
          };

          if (!tokenDetailsMapping[transactionInfo.address]) {
            const collectionData: ContractCall = {
              contractAddress: transactionInfo.address,
              methodCalls: [
                {
                  reference: "name",
                  methodName: "name",
                  methodParameters: [],
                },
                {
                  reference: "totalSupply",
                  methodName: "totalSupply",
                  methodParameters: [],
                },
                {
                  reference: "owner",
                  methodName: "owner",
                  methodParameters: [],
                },
              ],
            };

            const royaltyData: ContractCall = {
              contractAddress: this.royaltyFeeManager,
              methodCalls: [
                {
                  reference: "calculateRoyaltyFeeAndGetRecipient",
                  methodName: "calculateRoyaltyFeeAndGetRecipient",
                  methodParameters: [transactionInfo.address, "1", this.web3Instance.utils.toWei("1", "ether")],
                },
              ],
            };
            collectionMetadata.push(collectionData);
            royaltyMetadata.push(royaltyData);
            tokenDetailsMapping[transactionInfo.address] = true;
          }
          options.push(option);
        }
      }
      // this.logger.info("**************************** Get Contract Details ****************************");
      const { collectionInfos, tokenInfos } = await this.getContractDetails(options, collectionMetadata, royaltyMetadata);

      return { collectionInfo: collectionInfos, tokenInfo: tokenInfos };
    }
  }

  /**
   * Retrieves token standard information based on the provided transaction information and timestamp.
   *
   * @param transactionInfo - The transaction information object containing topics and data.
   * @param timestamp - The timestamp of the transaction, can be a number or a string.
   * @returns An object containing token standard information or undefined if the transaction is not ERC20, ERC721, or ERC1155.
   *
   * @throws Will log an error message if an exception occurs during the process.
   *
   * @example
   * const transactionInfo = {
   *   topics: [
   *     '0x...',
   *     '0x...',
   *     '0x...',
   *     '0x...'
   *   ],
   *   data: '0x...',
   *   address: '0x...'
   * };
   * const timestamp = Date.now();
   * const tokenInfo = await getTokenStandardInfo(transactionInfo, timestamp);
   * console.log(tokenInfo);
   */
  async getTokenStandardInfo(transactionInfo, timestamp: number | string) {
    try {
      let topicNumber: number;
      let interfaces = BlockChainConstant.ERC1155_INTERFACE; // This is send in supportsInterface method
      let tokenStandard = TokenStandard.ERC1155;
      let batch = false; //Represents if ERC1155 is single or batch
      let topic = "0";
      let value = "0";
      if (!transactionInfo.topics[3]) {
        // ERC20
        return undefined;
      }
      switch (transactionInfo.topics[0]) {
        case BlockChainConstant.ERC721_TRANSFER:
          topicNumber = 1;
          interfaces = BlockChainConstant.ERC721_INTERFACE;
          tokenStandard = TokenStandard.ERC721;
          topic = BigInt(transactionInfo.topics[3]).toString();
          value = "1";
          break;

        case BlockChainConstant.ERC1155_TRANSFER_SINGLE:
          topicNumber = 2;
          // transactionInfo.topics[topicNumber + 2] = transactionInfo.data.slice(0, 66);
          break;

        case BlockChainConstant.ERC1155_TRANSFER_BATCH:
          topicNumber = 2;
          batch = true;
          // transactionInfo.topics[topicNumber + 2] = transactionInfo.data.slice(0, 66);
          break;
      }
      if (topicNumber) {
        const currentOwnerIds = "0x" + transactionInfo.topics[topicNumber + 1].slice(-40); // topic will have For ERC1155 - 3, and ERC721 - 2
        const fromUserId = "0x" + transactionInfo.topics[topicNumber].slice(-40);
        const tokenStandardInfo = {
          topic, //ERC1155 - 4, and ERC721 - 3
          value,
          interfaces: interfaces,
          data: transactionInfo.data,
          batch: batch,
          createdAt: undefined,
          updatedAt: timestamp,
          currentOwnerIds,
          fromUserId: this.web3Instance.utils.toChecksumAddress(fromUserId),
          tokenStandard: tokenStandard,
          originalOwnerId: undefined,
          contractAddress: transactionInfo.address,
        };

        if (transactionInfo.topics[topicNumber] == 0) {
          tokenStandardInfo.createdAt = timestamp;
          tokenStandardInfo.originalOwnerId = this.web3Instance.utils.toChecksumAddress(currentOwnerIds);
        }
        return tokenStandardInfo;
      }
      return undefined;
    } catch (error) {
      this.logger.error(`getTokenStandardInfo ${error?.message}`);
    }
  }

  /**
   * Retrieves contract details including collection information and token information.
   *
   * @param args - The arguments required to fetch the contract details.
   * @param collectionMetadata - An array of ContractCall objects containing metadata for the collection.
   * @param royaltyMetadata - An array of ContractCall objects containing metadata for royalties.
   * @returns A promise that resolves to an object containing collectionInfos and tokenInfos.
   *          - collectionInfos: An array of collection information objects.
   *          - tokenInfos: An array of token information objects.
   * @throws Will log an error message if the operation fails.
   */
  async getContractDetails(args, collectionMetadata: ContractCall[], royaltyMetadata: ContractCall[]) {
    // let collectionInfos: any, tokenInfos: any;
    try {
      const collectionInfos = await this.getCollectionData(args, collectionMetadata, royaltyMetadata);
      const tokenInfos = await this.getNFTTokenInfo(args, collectionInfos);
      const tokenDetails = await this.getTokenDetails(tokenInfos);
      return {
        collectionInfos: collectionInfos ? collectionInfos : [],
        tokenInfos: tokenDetails && tokenDetails.length ? tokenDetails : [],
      };
    } catch (error) {
      this.logger.error(`getContractDetails ${error?.message}`);
    }
    return {
      collectionInfos: [],
      tokenInfos: [],
    };
  }

  /**
   * Retrieves collection data by performing multiple contract calls and parsing the results.
   *
   * @param args - An array of arguments containing transaction information, block number, token standard, creation date, and update date.
   * @param collectionMetadata - An array of contract calls for fetching collection metadata.
   * @param royaltyMetadata - An array of contract calls for fetching royalty metadata.
   * @returns A promise that resolves to an array of collection data objects or undefined if an error occurs.
   *
   * The function performs the following steps:
   * 1. Logs the start of the collection data retrieval process.
   * 2. Executes multiple contract calls in parallel to fetch royalty data and collection metadata.
   * 3. Iterates over the provided arguments to parse and construct collection data objects.
   * 4. Generates a slug for each collection name by transforming it to lowercase, replacing spaces with hyphens, removing non-word characters, and trimming extra hyphens.
   * 5. Constructs a collection data object with various properties including collection address, owner, name, slug, token standard, total supply, block number, statistics, royalty rate, royalty wallet, creation date, and update date.
   * 6. Pushes the constructed collection data object into the result array.
   * 7. Returns the result array containing collection data objects.
   * 8. Logs any errors that occur during the process and returns undefined.
   *
   * @throws Will log an error message if any error occurs during the process.
   */
  async getCollectionData(args, collectionMetadata: ContractCall[], royaltyMetadata: ContractCall[]) {
    try {
      // this.logger.info("**************************** Get Collection Data Multicall ****************************");
      const [royaltyData, metadata] = await Promise.all([
        this.multicallService.performMulticall(royaltyMetadata, false),
        this.multicallService.performMulticall(collectionMetadata),
      ]);
      const result = [];
      for (const option of args) {
        const { transactionInfo, block, tokenStandard, createdAt, updatedAt } = option;
        const collectionName = this.multicallService.parseResult(metadata, transactionInfo.address, "name");
        const owner = this.multicallService.parseResult(metadata, transactionInfo.address, "owner");
        const totalsupply = this.multicallService.parseResult(metadata, transactionInfo.address, "totalSupply");
        const royalty = this.multicallService.parseResult(royaltyData, this.royaltyFeeManager, "calculateRoyaltyFeeAndGetRecipient");

        const slug = (collectionName ? collectionName : "")
          .toString()
          .toLowerCase()
          .replace(/\s+/g, "-") // Replace spaces with -
          .replace(/[^\w\-]+/g, "") // Remove all non-word chars
          .replace(/\-\-+/g, "-") // Replace multiple - with single -
          .trim(); // Trim - from end of text

        const collectionData = {
          collectionAddress: transactionInfo?.address || null,
          owner: owner ? this.web3Instance.utils.toChecksumAddress(owner) : owner,
          name: collectionName,
          slug,
          tokenStandard,
          logo: null,
          totalsupply: totalsupply,
          blockNo: block,
          stats: {
            floorPrice: new mongoose.Types.Decimal128("0"),
            oneDaySale: new mongoose.Types.Decimal128("0"),
            oneDayChange: new mongoose.Types.Decimal128("0"),
            oneDayVolume: new mongoose.Types.Decimal128("0"),
            oneDayAvgPrice: new mongoose.Types.Decimal128("0"),
            sevenDaySale: new mongoose.Types.Decimal128("0"),
            sevenDayChange: new mongoose.Types.Decimal128("0"),
            sevenDayVolume: new mongoose.Types.Decimal128("0"),
            sevenDayAvgPrice: new mongoose.Types.Decimal128("0"),
            thirtyDaySale: new mongoose.Types.Decimal128("0"),
            thirtyDayChange: new mongoose.Types.Decimal128("0"),
            thirtyDayVolume: new mongoose.Types.Decimal128("0"),
            thirtyDayAvgPrice: new mongoose.Types.Decimal128("0"),
            totalVolume: new mongoose.Types.Decimal128("0"),
          },
          royaltyRate: royalty ? parseInt(royalty["1"].hex, 16) / Math.pow(10, 16) : null,
          royaltyWallet: royalty ? royalty["0"] : null,
          collectionCreatedAt: createdAt ? moment.unix(createdAt) : undefined,
          collectionUpdatedAt: updatedAt ? moment.unix(updatedAt) : undefined,
        };
        result.push({ type: EntityType.COLLECTION, insertedAt: updatedAt ? moment.unix(updatedAt) : undefined, blockNumber: block, collectionData, tokenData: null });
      }
      return result;
    } catch (error) {
      this.logger.error(`getCollectionData ${error?.message}`);
      return undefined;
    }
  }

  /**
   * Retrieves data from a smart contract function, with caching support.
   *
   * @param {string} functionName - The name of the smart contract function to call.
   * @param {any} contractInstance - The instance of the smart contract.
   * @param {any[] | null} [params=null] - Optional parameters to pass to the smart contract function.
   * @returns {Promise<any>} - The result of the smart contract function call, either from cache or directly from the contract.
   *
   * @throws {Error} - Throws an error if the function call fails.
   *
   * @example
   * ```typescript
   * const data = await getData('balanceOf', contractInstance, ['0x123...']);
   * ```
   *
   * @remarks
   * - The result is cached for 1 hour (3600 seconds) unless the function name is "totalSupply".
   * - If the data is found in the cache, it is returned directly without calling the contract.
   * - Logs an error message if the function call fails.
   */
  async getData(functionName: string, contractInstance, params = null) {
    try {
      const contractAddress = contractInstance._address;
      // const block = contractInstance.defaultBlock;
      const cacheKey = `${this.CONTRACT_DATA_PREFIX}${contractAddress}:${functionName}`;

      // Try to get from cache first
      const cachedData = await this.cacheManager.get(cacheKey);
      if (cachedData) {
        return cachedData;
      }
      // If not in cache, call contract
      let res = null;
      if (params && contractInstance.methods && contractInstance.methods[functionName]) {
        res = await contractInstance.methods[functionName](...params).call();
      } else if (contractInstance.methods && contractInstance.methods[functionName]) {
        res = await contractInstance.methods[functionName]().call();
      }

      // Cache the result
      if (res !== null && functionName !== "totalSupply") {
        await this.cacheManager.set(cacheKey, res, { ttl: 3600 });
      }

      return res;
    } catch (error) {
      this.logger.error(`getData ${functionName} ${error?.message}`);
      return null;
    }
  }

  /**
   * Retrieves NFT token information based on the provided arguments and collection information.
   *
   * @param args - An array of arguments containing transaction information and token standards.
   * @param collectionInfo - An array of collection information corresponding to the arguments.
   * @returns A promise that resolves to an array of processed NFT token information.
   *
   * This function processes each argument to decode ERC1155 token transfer data if applicable,
   * aggregates token values, and appends collection names to the result. If the token standard
   * is not ERC1155, it directly appends the argument to the result with the collection name.
   *
   * @throws Will log an error message if an exception occurs during processing.
   */
  async getNFTTokenInfo(args, collectionInfo) {
    try {
      const result = [];
      // this.logger.info("**************************** Get NFT TOKEN Data ****************************");
      for (const [index, arg] of args.entries()) {
        const { transactionInfo, tokenStandard } = arg;
        const tokens = {};
        /** If transaction topic is ERC115 transfer batch the decodeERC1155Data function is called */
        if (tokenStandard === TokenStandard.ERC1155) {
          const tokenTransfers = this.decodeERC1155Data(transactionInfo.data);
          // this.logger.info("**************************** DECODED DATA SUCCESS ****************************");
          for (const [innerIndex, tokenId] of tokenTransfers.ids.entries()) {
            if (!tokens[tokenId]) {
              tokens[tokenId] = "0";
            }
            // Convert existing token value to bigint
            const existingTokenValue = BigInt(tokens[tokenId]);

            // Convert transfer value to bigint
            const transferValue = BigInt(tokenTransfers.values[innerIndex]);

            // Add the values and update the `tokens` map with the new value as a string
            tokens[tokenId] = existingTokenValue + transferValue;
          }
          for (const [tokenId, value] of Object.entries(tokens)) {
            const tempArg = JSON.parse(JSON.stringify(arg));
            tempArg.topic = tokenId;
            tempArg.value = value;
            tempArg.collectionName = collectionInfo[index]?.collectionData?.name || null;
            result.push(tempArg);
          }
        } else {
          arg.collectionName = collectionInfo[index]?.collectionData?.name || null;
          result.push(arg);
        }
      }
      return result;
    } catch (error) {
      this.logger.error(`getNFTTokenInfo ${error?.message}`);
    }
  }

  /**
   * Decodes ERC-1155 data from a hexadecimal string.
   *
   * This function handles both single and batched transfers. For single transfers,
   * the data contains two 32-byte values representing the Token ID and Value. For
   * batched transfers, the data contains offsets and arrays of Token IDs and Values.
   *
   * @param data - The hexadecimal string representing the ERC-1155 data. It may start with "0x".
   * @returns An object containing arrays of Token IDs and Values.
   *
   * @example
   * ```typescript
   * const data = "0x...";
   * const result = decodeERC1155Data(data);
   * console.log(result.ids); // Array of Token IDs
   * console.log(result.values); // Array of Values
   * ```
   */
  decodeERC1155Data(data: string): { ids: string[]; values: string[] } {
    // Remove the "0x" prefix if present
    data = data.startsWith("0x") ? data.slice(2) : data;

    const hexToDecimal = (hex: string): number => parseInt(hex, 16);
    const bigIntFromHex = (hex: string): string => BigInt(`0x${hex}`).toString();

    // Helper to parse 32-byte chunks
    const getChunk = (offset: number): string => data.slice(offset, offset + 64);

    if (data.length === 128) {
      // Single transfer: data has two 32-byte values (Token ID and Value)
      const tokenId = bigIntFromHex(getChunk(0));
      const value = bigIntFromHex(getChunk(64));
      return {
        ids: [tokenId],
        values: [value],
      };
    } else {
      // Batched transfer: data contains offsets and arrays
      const idsOffset = hexToDecimal(getChunk(0)) * 2; // Multiply by 2 for byte indexing
      const valuesOffset = hexToDecimal(getChunk(64)) * 2;

      // Decode `ids`
      const idsLength = hexToDecimal(getChunk(idsOffset));
      const ids: string[] = [];
      for (let i = 0; i < idsLength; i++) {
        ids.push(bigIntFromHex(getChunk(idsOffset + 64 + i * 64)));
      }

      // Decode `values`
      const valuesLength = hexToDecimal(getChunk(valuesOffset));
      const values: string[] = [];
      for (let i = 0; i < valuesLength; i++) {
        values.push(bigIntFromHex(getChunk(valuesOffset + 64 + i * 64)));
      }

      return { ids, values };
    }
  }

  /**
   * Retrieves the token URL and metadata from a given URL.
   *
   * This function processes the input URL to determine if it needs to make an HTTP request
   * to fetch the token metadata or if the metadata can be directly extracted from the URL.
   * It supports various URL formats including IPFS, base64 encoded JSON, and HTTP URLs.
   *
   * @param {string | null} url - The input URL which can be a string or null.
   * @returns {Promise<{ url: string; axiosCall: boolean; tokenMetadata: any }>}
   *          An object containing the processed URL, a flag indicating if an HTTP request is needed,
   *          and the token metadata if available.
   *
   * @property {string} url - The processed token URL.
   * @property {boolean} axiosCall - A flag indicating if an HTTP request is needed to fetch the token metadata.
   * @property {any} tokenMetadata - The token metadata extracted from the URL if available.
   *
   * @example
   * // Example usage:
   * const result = await getTokenUrl('ipfs://Qm...');
   * // result: { url: 'https://gateway.ipfs.io/ipfs/Qm...', axiosCall: true, tokenMetadata: {} }
   *
   * @example
   * const result = await getTokenUrl('data:application/json;base64,eyJrZXkiOiJ2YWx1ZSJ9');
   * // result: { url: 'data:application/json;base64,eyJrZXkiOiJ2YWx1ZSJ9', axiosCall: false, tokenMetadata: { key: 'value' } }
   */
  async getTokenUrl(url: string | null): Promise<{ url: string; axiosCall: boolean; tokenMetadata: any }> {
    // Early return for null/empty URLs
    if (!url) {
      return { url: "", axiosCall: false, tokenMetadata: {} };
    }

    let tokenUrl: string = url;
    let axiosCall = false;
    let tokenMetadata: any = {};

    try {
      if (url.startsWith("data:application/json;base64,")) {
        const base64Part = url.substring("data:application/json;base64,".length);
        if (base64Part) {
          try {
            // Cross-platform base64 decoding
            const bufferString = this.decodeBase64(base64Part);
            tokenMetadata = JSON.parse(bufferString, (key, value) => {
              if (typeof value === "number" && !Number.isSafeInteger(value)) {
                return BigInt(value).toString();
              }
              return value;
            });
          } catch (parseError) {
            this.logger.error(`Failed to parse base64 JSON from URL: ${url}`, parseError);
            // Keep tokenMetadata as empty object on error
          }
        }
      } else if (url.startsWith("ipfs://")) {
        const ipfsPath = url.substring(7); // Remove "ipfs://" prefix
        if (ipfsPath) {
          tokenUrl = this.gateway + ipfsPath;
          axiosCall = true;
        } else {
          this.logger.warn(`Malformed IPFS URL: ${url}`);
          tokenUrl = url;
          axiosCall = true;
        }
      } else if (url.startsWith("http://") || url.startsWith("https://")) {
        axiosCall = true;
      } else {
        tokenUrl = this.gateway + url;
        axiosCall = true;
      }
    } catch (error) {
      this.logger.error(`Unexpected error processing URL: ${url}`, error);
      tokenUrl = url;
      axiosCall = true;
    }
    return { url: tokenUrl, axiosCall, tokenMetadata };
  }

  private decodeBase64(base64String: string): string {
    if (typeof Buffer !== "undefined") {
      return Buffer.from(base64String, "base64").toString("utf8");
    } else if (typeof atob !== "undefined") {
      return atob(base64String);
    } else {
      throw new Error("No base64 decoding method available");
    }
  }

  // Enhanced function to handle IPFS URLs and data URLs
  getMimeTypeFromUrl(url) {
    if (!url || typeof url !== "string") return null;

    // Check if it's a data URL (data:image/png;base64,...)
    if (url.startsWith("data:")) {
      const mimeMatch = url.match(/^data:([^;,]+)/);
      return mimeMatch ? mimeMatch[1] : null;
    }

    // Extract extension from URL
    const extension = url.split(/[#?]/)[0].split(".").pop()?.trim().toLowerCase();

    // Handle IPFS URLs
    if (url.includes("ipfs://") || url.includes("/ipfs/")) {
      // If there's an extension, use it to determine MIME type
      if (extension) {
        // Use the extension mapping below
      } else {
        // Default to application/octet-stream for unknown IPFS content
        return "application/octet-stream";
      }
    }

    if (!extension) return null;

    // Map common extensions to MIME types
    const mimeTypeMap = {
      jpg: "image/jpeg",
      jpeg: "image/jpeg",
      png: "image/png",
      gif: "image/gif",
      svg: "image/svg+xml",
      webp: "image/webp",
      mp4: "video/mp4",
      webm: "video/webm",
      mp3: "audio/mpeg",
      wav: "audio/wav",
      pdf: "application/pdf",
      json: "application/json",
      html: "text/html",
      glb: "model/gltf-binary",
      gltf: "model/gltf+json",
    };

    return mimeTypeMap[extension] || `application/${extension}`;
  }

  /**
   * Retrieves token details including metadata and royalty information for a given set of arguments.
   *
   * @param {Array} args - An array of objects containing information about the tokens to retrieve details for.
   * @param {Object} args[].topic - The token ID.
   * @param {Object} args[].transactionInfo - Information about the transaction.
   * @param {string} args[].transactionInfo.address - The address of the token contract.
   * @param {string} args[].transactionInfo.transactionHash - The transaction hash.
   * @param {string} args[].tokenStandard - The standard of the token (e.g., ERC721).
   * @param {number} args[].createdAt - The creation timestamp of the token.
   * @param {number} args[].updatedAt - The update timestamp of the token.
   * @param {string} args[].fromUserId - The ID of the user who initiated the transaction.
   * @param {number} args[].block - The block number of the transaction.
   * @param {string} args[].currentOwnerIds - The current owner IDs of the token.
   * @param {string} args[].originalOwnerId - The original owner ID of the token.
   * @param {BigInt} [args[].value=BigInt(1)] - The value of the token.
   *
   * @returns {Promise<Array>} A promise that resolves to an array of token details including metadata and royalty information.
   *
   * @throws Will throw an error if the token details cannot be retrieved.
   *
   * @example
   * const args = [
   *   {
   *     topic: '1',
   *     transactionInfo: {
   *       address: '0x1234567890abcdef',
   *       transactionHash: '0xabcdef1234567890'
   *     },
   *     tokenStandard: 'ERC721',
   *     createdAt: 1622547800,
   *     updatedAt: 1622547900,
   *     fromUserId: 'user123',
   *     block: 123456,
   *     currentOwnerIds: '0xabcdef1234567890',
   *     originalOwnerId: 'user456',
   *     value: BigInt(1)
   *   }
   * ];
   *
   * getTokenDetails(args).then(tokenDetails => {
   *   console.log(tokenDetails);
   * }).catch(error => {
   *   console.error(error);
   * });
   */
  async getTokenDetails(args) {
    try {
      const multicallTokenURIs = [];
      const multicallRoyalty = [];
      const axioCallData = {};
      const axiosRequest = [];
      const finalResult = [];
      let axiosResponse: any[];

      for (const arg of args) {
        const { topic, transactionInfo, tokenStandard } = arg;
        const functionName = tokenStandard == TokenStandard.ERC721 ? "tokenURI" : "uri";
        const tokenId = topic;
        const urlData: ContractCall = {
          reference: `${transactionInfo.address}_${tokenId}`,
          contractAddress: transactionInfo.address,
          methodCalls: [
            {
              reference: "tokenUrl",
              methodName: functionName,
              methodParameters: [tokenId],
            },
          ],
        };
        const royaltyData: ContractCall = {
          reference: `${transactionInfo.address}_${tokenId}`,
          contractAddress: this.royaltyFeeManager,
          methodCalls: [
            {
              reference: "calculateRoyaltyFeeAndGetRecipient",
              methodName: "calculateRoyaltyFeeAndGetRecipient",
              methodParameters: [transactionInfo.address, tokenId, this.web3Instance.utils.toWei("1", "ether")],
            },
          ],
        };
        multicallTokenURIs.push(urlData);
        multicallRoyalty.push(royaltyData);
      }

      // this.logger.info("**************************** Get TOKEN URL AND ROYALTY INFO ****************************");

      const multicallURIResponses = await this.multicallService.performMulticall(multicallTokenURIs);
      const multicallRoyaltyResponses = await this.multicallService.performMulticall(multicallRoyalty, false);
      for (const value of args) {
        const { topic } = value;
        const tokenId = topic;
        const key = `${value.transactionInfo.address}_${tokenId}`;
        const tokenUrl = this.multicallService.parseResult(multicallURIResponses, key, "tokenUrl");
        const data = await this.getTokenUrl(tokenUrl);
        data["royalty"] = this.multicallService.parseResult(multicallRoyaltyResponses, key, "calculateRoyaltyFeeAndGetRecipient");
        axioCallData[key] = data;

        if (data.axiosCall) {
          // Create axios request with timeout and proper error handling
          axiosRequest.push(
            axios
              .get(data.url, {
                timeout: 10000, // 10 second timeout
                maxRedirects: 3,
                headers: {
                  "User-Agent": "NFT-Indexer/1.0",
                },
                // Prevent memory leaks by limiting response size
                maxContentLength: 10 * 1024 * 1024, // 10MB max
                maxBodyLength: 10 * 1024 * 1024, // 10MB max
              })
              .catch(error => {
                this.logger.warn(`Axios request failed for ${data.url}: ${error.message}`);
                return null;
              }),
          );
        } else {
          axiosRequest.push(Promise.resolve(null));
        }
      }

      // Use Promise.allSettled with timeout to prevent hanging requests
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const requestTimeout = new Promise((_, reject) => setTimeout(() => reject(new Error("Axios requests timeout")), 30000));

      try {
        await Promise.race([
          Promise.allSettled(axiosRequest).then(results => {
            axiosResponse = results.map(result => (result.status === "fulfilled" ? result.value : null));
          }),
          requestTimeout,
        ]);
      } catch (error) {
        this.logger.error(`Axios requests failed: ${error.message}`);
        axiosResponse = new Array(axiosRequest.length).fill(null);
      }
      // this.logger.info("**************************** Get TOKEN METADATA AXIOS ****************************");
      for (const [index, arg] of args.entries()) {
        const { transactionInfo, tokenStandard, topic, createdAt, updatedAt, fromUserId, block, currentOwnerIds, originalOwnerId, value = BigInt(1) } = arg;
        const tokenId = topic;
        const key = `${arg.transactionInfo.address}_${tokenId}`;
        const tokenMetaData = axiosResponse[index]?.data ? axiosResponse[index].data : axioCallData[key].tokenMetadata;

        const name = tokenMetaData?.name ? tokenMetaData?.name : arg?.collectionName ? arg?.collectionName + ` #${tokenId}` : "";
        const description = tokenMetaData?.description || null;
        const imageUrl = tokenMetaData?.image || tokenMetaData?.image_url;
        let tempUrl = imageUrl && imageUrl.split(":").includes("ipfs") ? this.gateway + imageUrl.split("//")[1] : imageUrl;

        const validateCID = this.CID_REGEX.test(tempUrl);

        if (validateCID) {
          tempUrl = this.gateway + tempUrl;
        }

        if (tempUrl && tempUrl.includes("https://ipfs.io/ipfs/")) {
          tempUrl.replace("https://ipfs.io/ipfs/", this.gateway);
        }

        const logo = {
          url: tempUrl || null,
          contentType: imageUrl ? this.getMimeTypeFromUrl(imageUrl) : null,
          animationUrl: tokenMetaData?.animation_url || tokenMetaData?.youtube_url || null,
        };
        const properties =
          (tokenMetaData?.attributes || []).map(attribute => {
            let value = attribute?.value;
            if (typeof value === "number" || typeof value === "bigint") {
              value = value.toString();
            }

            return { traitType: attribute?.trait_type, value };
          }) || [];

        const royalty = axioCallData[key]["royalty"];
        // this.logger.info("**************************** REFACTOR IMAGE URL AND PROPERTIES ****************************");
        const tokenData = {
          name,
          description,
          logo,
          properties,
          price: 0,
          tokenId,
          isHidden: logo.url ? false : true,
          tokenStandard,
          fromUserId,
          currentOwnerIds: [
            {
              walletAddress: this.web3Instance.utils.toChecksumAddress(currentOwnerIds),
              qty: new mongoose.Types.Decimal128(value.toString()),
            },
          ],
          blockNo: block,
          collectionAddress: transactionInfo?.address || null,
          transactionHash: transactionInfo?.transactionHash,
          tokenCreatedAt: createdAt ? moment.unix(createdAt) : undefined,
          tokenUpdatedAt: updatedAt ? moment.unix(updatedAt) : undefined,
          royaltyRate: royalty ? parseInt(royalty["1"].hex, 16) / Math.pow(10, 16) : null,
          royaltyWallet: royalty ? royalty["0"] : null,
        };

        if (originalOwnerId) tokenData["originalOwnerId"] = originalOwnerId;

        finalResult.push({ type: EntityType.TOKEN, insertedAt: updatedAt ? moment.unix(updatedAt) : undefined, blockNumber: block, collectionData: null, tokenData });
      }
      return finalResult;
    } catch (error) {
      this.logger.error(`getTokenDetails ${error?.message}`);
    }
  }

  /**
   * Saves the provided collection and token data into the database.
   *
   * @param collectionResult - The collection data to be saved.
   * @param tokenResult - The token data to be saved.
   * @returns {Promise<void>} A promise that resolves when the data has been successfully saved.
   *
   * @throws Will log an error message if there is an issue while saving the data.
   */
  async saveCollectionAndToken(collectionResult, tokenResult) {
    try {
      await this.queueService.saveCollectionAndToken(collectionResult, tokenResult);
      this.logger.info(`Worker ${this.workerId} processed ${collectionResult.length} collections and ${tokenResult.length} tokens`);
    } catch (error) {
      this.logger.error(`Error while processing data in worker ${this.workerId}: ${error?.message}`);
    }
  }
}

// Trigger the worker task if in a worker thread
if (parentPort && workerData) {
  const { blockStart, blockSize, workerId, dbUri } = workerData;

  let mongoConnection: typeof mongoose | null = null;
  let task: WorkerService | null = null;

  // Graceful shutdown handlers
  const shutdown = async (signal: string) => {
    // eslint-disable-next-line no-console
    console.log(`Worker ${workerId}: Received ${signal}, shutting down gracefully...`);

    try {
      // Cleanup task instance if it exists
      if (task && typeof task.cleanup === "function") {
        await task.cleanup();
      }

      // Close MongoDB connection gracefully
      if (mongoConnection) {
        try {
          await mongoConnection.connection.close(true);
          console.log(`Worker ${workerId}: MongoDB connection closed during shutdown`);
          await new Promise(resolve => setTimeout(resolve, 50));
        } catch (error) {
          console.error(`Worker ${workerId}: Error closing MongoDB during shutdown:`, error);
        } finally {
          mongoConnection = null;
        }
      }

      // Log memory usage at shutdown
      const memUsage = process.memoryUsage();
      // eslint-disable-next-line no-console
      console.log(`Worker ${workerId}: Memory at shutdown:`, {
        rss: Math.round(memUsage.rss / 1024 / 1024) + "MB",
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + "MB",
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + "MB",
        external: Math.round(memUsage.external / 1024 / 1024) + "MB",
        arrayBuffers: Math.round(memUsage.arrayBuffers / 1024 / 1024) + "MB",
      });

      // Force garbage collection before exit
      if (global.gc) {
        console.log(`Worker ${workerId}: Final garbage collection`);
        global.gc();
      }
    } catch (error) {
      console.error(`Worker ${workerId}: Error during shutdown:`, error);
    }

    // Log active resources (safer alternative to _getActiveHandles and _getActiveRequests)
    // eslint-disable-next-line no-console
    console.log(`Worker ${workerId}: Checking for active resources before shutdown`);

    try {
      // Properly disconnect MongoDB connection if it exists
      if (mongoConnection) {
        try {
          await mongoConnection.disconnect();
          // eslint-disable-next-line no-console
          console.log(`Worker ${workerId}: MongoDB connection closed`);
        } catch (disconnectError) {
          // eslint-disable-next-line no-console
          console.error(`Worker ${workerId}: Error disconnecting MongoDB:`, disconnectError);
          // Force close the connection
          try {
            await mongoConnection.connection.close();
          } catch (closeError) {
            // eslint-disable-next-line no-console
            console.error(`Worker ${workerId}: Error force closing MongoDB:`, closeError);
          }
        }
        mongoConnection = null;
      }

      // Force garbage collection if available
      if (global.gc) {
        // eslint-disable-next-line no-console
        console.log(`Worker ${workerId}: Forcing garbage collection before exit`);
        global.gc();
      }

      // eslint-disable-next-line no-console
      console.log(`Worker ${workerId}: Cleanup completed, exiting...`);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Worker ${workerId}: Error during shutdown:`, error);
    }

    // Use appropriate exit code based on signal
    let exitCode = 0;
    if (signal === "SIGSEGV") {
      exitCode = 139;
    } else if (signal === "SIGABRT") {
      exitCode = 134;
    }

    // Exit gracefully with a small delay to allow cleanup to complete
    setTimeout(() => {
      process.exit(exitCode);
    }, 200);
  };

  // Additional safety measures for native module crashes
  process.on("SIGSEGV", error => {
    // eslint-disable-next-line no-console
    console.error(`Worker ${workerId}: Segmentation fault detected:`, error);

    // Log memory usage at time of crash
    const memUsage = process.memoryUsage();
    // eslint-disable-next-line no-console
    console.error(`Worker ${workerId}: Memory at crash:`, {
      rss: Math.round(memUsage.rss / 1024 / 1024) + "MB",
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + "MB",
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + "MB",
      external: Math.round(memUsage.external / 1024 / 1024) + "MB",
      arrayBuffers: Math.round(memUsage.arrayBuffers / 1024 / 1024) + "MB",
    });

    // Log current call stack
    // eslint-disable-next-line no-console
    console.error(`Worker ${workerId}: Call stack:`, new Error().stack);

    void shutdown("SIGSEGV");
  });

  process.on("SIGABRT", error => {
    // eslint-disable-next-line no-console
    console.error(`Worker ${workerId}: Abort signal detected:`, error);

    // Log memory usage at time of crash
    const memUsage = process.memoryUsage();
    // eslint-disable-next-line no-console
    console.error(`Worker ${workerId}: Memory at abort:`, {
      rss: Math.round(memUsage.rss / 1024 / 1024) + "MB",
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + "MB",
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + "MB",
      external: Math.round(memUsage.external / 1024 / 1024) + "MB",
      arrayBuffers: Math.round(memUsage.arrayBuffers / 1024 / 1024) + "MB",
    });

    // Log current call stack
    // eslint-disable-next-line no-console
    console.error(`Worker ${workerId}: Call stack:`, new Error().stack);

    void shutdown("SIGABRT");
  });

  process.on("SIGTERM", () => {
    void shutdown("SIGTERM");
  });
  process.on("SIGINT", () => {
    void shutdown("SIGINT");
  });
  process.on("uncaughtException", error => {
    // eslint-disable-next-line no-console
    console.error(`Worker ${workerId}: Uncaught exception:`, error);
    void shutdown("uncaughtException");
  });
  process.on("unhandledRejection", (reason, promise) => {
    // eslint-disable-next-line no-console
    console.error(`Worker ${workerId}: Unhandled rejection at:`, promise, "reason:", reason);
    void shutdown("unhandledRejection");
  });

  mongoose.set("strictQuery", false);

  // Create connection first and wait for it to be ready
  mongoose
    .connect(dbUri, {
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      // Add connection pool settings for better memory management
      maxPoolSize: 2, // Reduced pool size for workers
      minPoolSize: 1,
      maxIdleTimeMS: 30000,
    })
    .then(async () => {
      // eslint-disable-next-line no-console
      console.log(`Worker ${workerId}: MongoDB connection established`);
      mongoConnection = mongoose;

      const blockDataModel = mongoose.model<BlockDataDocument>(BlockData.name, BlockDataSchema, "blockdatas");

      const queueService = new QueueService(blockDataModel);
      task = new WorkerService(blockStart, blockSize, workerId, queueService);

      await task.run();

      // Properly disconnect MongoDB after task completion with safer cleanup
      if (mongoConnection) {
        try {
          // Force close all connections in the pool
          await mongoConnection.connection.close(true);
          // eslint-disable-next-line no-console
          console.log(`Worker ${workerId}: Task completed, MongoDB connection closed`);

          // Add a small delay to allow cleanup to complete
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (disconnectError) {
          // eslint-disable-next-line no-console
          console.error(`Worker ${workerId}: Error disconnecting MongoDB:`, disconnectError);
        } finally {
          mongoConnection = null;
        }
      }
    })
    .catch(err => {
      // eslint-disable-next-line no-console
      console.error(`Worker ${workerId}: MongoDB connection error:`, err);
      parentPort.postMessage({ error: err.message, workerId });
      process.exit(1);
    });
}
