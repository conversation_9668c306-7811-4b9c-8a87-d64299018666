import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import * as http from 'http';
import * as https from 'https';

@Injectable()
export class HttpClientService {
  private readonly logger = new Logger(HttpClientService.name);
  private readonly axiosInstance: AxiosInstance;
  private activeRequests = new Set<Promise<any>>();
  private requestCount = 0;

  constructor() {
    // Create HTTP agents with connection pooling and limits
    const httpAgent = new http.Agent({
      keepAlive: true,
      maxSockets: 10,
      maxFreeSockets: 5,
      timeout: 30000,
    });

    const httpsAgent = new https.Agent({
      keepAlive: true,
      maxSockets: 10,
      maxFreeSockets: 5,
      timeout: 30000,
    });

    this.axiosInstance = axios.create({
      timeout: 10000, // 10 second timeout
      maxRedirects: 3,
      maxContentLength: 10 * 1024 * 1024, // 10MB max
      maxBodyLength: 10 * 1024 * 1024, // 10MB max
      httpAgent,
      httpsAgent,
      headers: {
        'User-Agent': 'NFT-Indexer/1.0',
        'Connection': 'keep-alive',
      },
    });

    // Add request interceptor for tracking
    this.axiosInstance.interceptors.request.use(
      (config) => {
        this.requestCount++;
        return config;
      },
      (error) => {
        this.logger.error(`Request interceptor error: ${error.message}`);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for cleanup
    this.axiosInstance.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        this.logger.warn(`HTTP request failed: ${error.message}`);
        return Promise.reject(error);
      }
    );
  }

  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T | null> {
    if (!url) {
      return null;
    }

    const requestPromise = this.makeRequest<T>('GET', url, undefined, config);
    this.activeRequests.add(requestPromise);

    try {
      const result = await requestPromise;
      return result;
    } finally {
      this.activeRequests.delete(requestPromise);
    }
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T | null> {
    if (!url) {
      return null;
    }

    const requestPromise = this.makeRequest<T>('POST', url, data, config);
    this.activeRequests.add(requestPromise);

    try {
      const result = await requestPromise;
      return result;
    } finally {
      this.activeRequests.delete(requestPromise);
    }
  }

  private async makeRequest<T>(
    method: 'GET' | 'POST',
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T | null> {
    try {
      let response: AxiosResponse<T>;

      if (method === 'GET') {
        response = await this.axiosInstance.get<T>(url, config);
      } else {
        response = await this.axiosInstance.post<T>(url, data, config);
      }

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          this.logger.warn(`Request timeout for ${url}`);
        } else if (error.response?.status === 404) {
          this.logger.warn(`Resource not found: ${url}`);
        } else if (error.response?.status && error.response.status >= 500) {
          this.logger.warn(`Server error ${error.response.status} for ${url}`);
        } else {
          this.logger.warn(`HTTP error for ${url}: ${error.message}`);
        }
      } else {
        this.logger.error(`Unexpected error for ${url}: ${error.message}`);
      }
      return null;
    }
  }

  async batchGet<T = any>(urls: string[], config?: AxiosRequestConfig): Promise<(T | null)[]> {
    if (!urls || urls.length === 0) {
      return [];
    }

    // Limit concurrent requests to prevent memory issues
    const BATCH_SIZE = 10;
    const results: (T | null)[] = [];

    for (let i = 0; i < urls.length; i += BATCH_SIZE) {
      const batch = urls.slice(i, i + BATCH_SIZE);
      const batchPromises = batch.map(url => this.get<T>(url, config));

      try {
        // Use Promise.allSettled to handle individual failures
        const batchResults = await Promise.allSettled(batchPromises);
        const batchData = batchResults.map(result => 
          result.status === 'fulfilled' ? result.value : null
        );
        results.push(...batchData);
      } catch (error) {
        this.logger.error(`Batch request error: ${error.message}`);
        // Fill with nulls for failed batch
        results.push(...new Array(batch.length).fill(null));
      }

      // Small delay between batches to prevent overwhelming the server
      if (i + BATCH_SIZE < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return results;
  }

  async cancelAllRequests(): Promise<void> {
    this.logger.warn(`Cancelling ${this.activeRequests.size} active requests`);
    
    // Note: Axios doesn't provide a direct way to cancel all requests
    // This is more of a cleanup notification
    this.activeRequests.clear();
  }

  getActiveRequestCount(): number {
    return this.activeRequests.size;
  }

  getTotalRequestCount(): number {
    return this.requestCount;
  }

  getStats(): {
    activeRequests: number;
    totalRequests: number;
  } {
    return {
      activeRequests: this.activeRequests.size,
      totalRequests: this.requestCount,
    };
  }

  // Cleanup method for graceful shutdown
  async cleanup(): Promise<void> {
    try {
      await this.cancelAllRequests();
      
      // Close HTTP agents
      if (this.axiosInstance.defaults.httpAgent) {
        (this.axiosInstance.defaults.httpAgent as http.Agent).destroy();
      }
      if (this.axiosInstance.defaults.httpsAgent) {
        (this.axiosInstance.defaults.httpsAgent as https.Agent).destroy();
      }

      this.logger.log('HTTP client cleanup completed');
    } catch (error) {
      this.logger.error(`HTTP client cleanup error: ${error.message}`);
    }
  }
}
