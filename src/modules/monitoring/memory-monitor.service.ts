import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as os from 'os';

interface MemorySnapshot {
  timestamp: Date;
  rss: number;
  heapTotal: number;
  heapUsed: number;
  external: number;
  arrayBuffers: number;
  systemFree: number;
  systemTotal: number;
}

@Injectable()
export class MemoryMonitorService {
  private readonly logger = new Logger(MemoryMonitorService.name);
  private memoryHistory: MemorySnapshot[] = [];
  private readonly MAX_HISTORY = 100;
  private readonly MEMORY_WARNING_THRESHOLD = 1024 * 1024 * 1024; // 1GB
  private readonly MEMORY_CRITICAL_THRESHOLD = 1.5 * 1024 * 1024 * 1024; // 1.5GB
  private readonly MEMORY_GROWTH_THRESHOLD = 100 * 1024 * 1024; // 100MB growth

  @Cron('*/30 * * * * *') // Every 30 seconds
  async monitorMemory(): Promise<void> {
    try {
      const snapshot = this.takeMemorySnapshot();
      this.memoryHistory.push(snapshot);

      // Keep only recent history
      if (this.memoryHistory.length > this.MAX_HISTORY) {
        this.memoryHistory = this.memoryHistory.slice(-this.MAX_HISTORY);
      }

      // Check for memory issues
      this.checkMemoryThresholds(snapshot);
      this.checkMemoryGrowth();

    } catch (error) {
      this.logger.error(`Memory monitoring error: ${error.message}`);
    }
  }

  private takeMemorySnapshot(): MemorySnapshot {
    const memUsage = process.memoryUsage();
    const systemMem = {
      free: os.freemem(),
      total: os.totalmem(),
    };

    return {
      timestamp: new Date(),
      rss: memUsage.rss,
      heapTotal: memUsage.heapTotal,
      heapUsed: memUsage.heapUsed,
      external: memUsage.external,
      arrayBuffers: memUsage.arrayBuffers,
      systemFree: systemMem.free,
      systemTotal: systemMem.total,
    };
  }

  private checkMemoryThresholds(snapshot: MemorySnapshot): void {
    const rssMB = Math.round(snapshot.rss / 1024 / 1024);
    const heapUsedMB = Math.round(snapshot.heapUsed / 1024 / 1024);
    const systemFreeMB = Math.round(snapshot.systemFree / 1024 / 1024);

    if (snapshot.rss > this.MEMORY_CRITICAL_THRESHOLD) {
      this.logger.error(`🚨 CRITICAL MEMORY USAGE: ${rssMB}MB RSS, ${heapUsedMB}MB heap, ${systemFreeMB}MB system free`);
      
      // Force garbage collection
      if (global.gc) {
        this.logger.warn('Forcing garbage collection due to critical memory usage');
        global.gc();
      }
      
      // Log top memory consumers
      this.logMemoryDetails();
      
    } else if (snapshot.rss > this.MEMORY_WARNING_THRESHOLD) {
      this.logger.warn(`⚠️ High memory usage: ${rssMB}MB RSS, ${heapUsedMB}MB heap, ${systemFreeMB}MB system free`);
    }

    // Check system memory
    if (snapshot.systemFree < 512 * 1024 * 1024) { // Less than 512MB free
      this.logger.error(`🚨 LOW SYSTEM MEMORY: Only ${systemFreeMB}MB free`);
    }
  }

  private checkMemoryGrowth(): void {
    if (this.memoryHistory.length < 2) return;

    const current = this.memoryHistory[this.memoryHistory.length - 1];
    const previous = this.memoryHistory[this.memoryHistory.length - 2];
    
    const growth = current.rss - previous.rss;
    
    if (growth > this.MEMORY_GROWTH_THRESHOLD) {
      const growthMB = Math.round(growth / 1024 / 1024);
      this.logger.warn(`📈 Memory growth detected: +${growthMB}MB in 30 seconds`);
      
      // Check for sustained growth over last 5 snapshots
      if (this.memoryHistory.length >= 5) {
        const last5 = this.memoryHistory.slice(-5);
        const totalGrowth = last5[4].rss - last5[0].rss;
        
        if (totalGrowth > this.MEMORY_GROWTH_THRESHOLD * 3) {
          const totalGrowthMB = Math.round(totalGrowth / 1024 / 1024);
          this.logger.error(`🚨 SUSTAINED MEMORY LEAK: +${totalGrowthMB}MB over 2.5 minutes`);
          this.logMemoryDetails();
        }
      }
    }
  }

  private logMemoryDetails(): void {
    try {
      const snapshot = this.takeMemorySnapshot();
      
      this.logger.error('=== MEMORY DETAILS ===');
      this.logger.error(`RSS: ${Math.round(snapshot.rss / 1024 / 1024)}MB`);
      this.logger.error(`Heap Total: ${Math.round(snapshot.heapTotal / 1024 / 1024)}MB`);
      this.logger.error(`Heap Used: ${Math.round(snapshot.heapUsed / 1024 / 1024)}MB`);
      this.logger.error(`External: ${Math.round(snapshot.external / 1024 / 1024)}MB`);
      this.logger.error(`Array Buffers: ${Math.round(snapshot.arrayBuffers / 1024 / 1024)}MB`);
      this.logger.error(`System Free: ${Math.round(snapshot.systemFree / 1024 / 1024)}MB`);
      this.logger.error(`System Total: ${Math.round(snapshot.systemTotal / 1024 / 1024)}MB`);
      
      // Log CPU usage
      const cpus = os.cpus();
      this.logger.error(`CPU Cores: ${cpus.length}`);
      this.logger.error(`Load Average: ${os.loadavg().map(l => l.toFixed(2)).join(', ')}`);
      
    } catch (error) {
      this.logger.error(`Error logging memory details: ${error.message}`);
    }
  }

  public getMemoryHistory(): MemorySnapshot[] {
    return [...this.memoryHistory];
  }

  public getCurrentMemoryUsage(): MemorySnapshot {
    return this.takeMemorySnapshot();
  }

  public forceGarbageCollection(): void {
    if (global.gc) {
      this.logger.log('Forcing garbage collection manually');
      global.gc();
    } else {
      this.logger.warn('Garbage collection not available (run with --expose-gc)');
    }
  }

  public getMemoryStats(): {
    current: MemorySnapshot;
    peak: MemorySnapshot;
    average: Partial<MemorySnapshot>;
  } {
    const current = this.takeMemorySnapshot();
    
    if (this.memoryHistory.length === 0) {
      return {
        current,
        peak: current,
        average: current,
      };
    }

    const peak = this.memoryHistory.reduce((max, snapshot) => 
      snapshot.rss > max.rss ? snapshot : max
    );

    const average = {
      rss: this.memoryHistory.reduce((sum, s) => sum + s.rss, 0) / this.memoryHistory.length,
      heapUsed: this.memoryHistory.reduce((sum, s) => sum + s.heapUsed, 0) / this.memoryHistory.length,
      heapTotal: this.memoryHistory.reduce((sum, s) => sum + s.heapTotal, 0) / this.memoryHistory.length,
    };

    return { current, peak, average };
  }
}
